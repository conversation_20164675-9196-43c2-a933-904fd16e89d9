@import '~antd/es/style/themes/default.less';
@import '../../../utils/utils.less';

.standardList {
  :global {
    .ant-card-head {
      border-bottom: none;
    }
    .ant-card-head-title {
      padding: 24px 0;
      line-height: 32px;
    }
    .ant-card-extra {
      padding: 24px 0;
    }
    .ant-list-pagination {
      margin-top: 24px;
      text-align: right;
    }
    .ant-avatar-lg {
      width: 48px;
      height: 48px;
      line-height: 48px;
    }
  }
  .headerInfo {
    position: relative;
    text-align: center;
    & > span {
      display: inline-block;
      margin-bottom: 4px;
      color: @text-color-secondary;
      font-size: @font-size-base;
      line-height: 22px;
    }
    & > p {
      margin: 0;
      color: @heading-color;
      font-size: 24px;
      line-height: 32px;
    }
    & > em {
      position: absolute;
      top: 0;
      right: 0;
      width: 1px;
      height: 56px;
      background-color: @border-color-split;
    }
  }
  .listContent {
    font-size: 0;
    .listContentItem {
      display: inline-block;
      margin-left: 80px;
      color: @text-color-secondary;
      font-size: @font-size-base;
      vertical-align: middle;
      > span {
        line-height: 20px;
      }
      > p {
        margin-top: 4px;
        margin-bottom: 0;
        line-height: 22px;
      }
    }
  }
  .extraContentSearch {
    width: 272px;
    margin-left: 16px;
  }
}

@media screen and (max-width: @screen-xs) {
  .standardList {
    :global {
      .ant-list-item-content {
        display: block;
        flex: none;
        width: 100%;
      }
      .ant-list-item-action {
        margin-left: 0;
      }
    }
    .listContent {
      margin-left: 0;
      & > div {
        margin-left: 0;
      }
    }
    .listCard {
      :global {
        .ant-card-head-title {
          overflow: visible;
        }
      }
    }
  }
}

@media screen and (max-width: @screen-sm) {
  .standardList {
    .extraContentSearch {
      width: 100%;
      margin-left: 0;
    }
    .headerInfo {
      margin-bottom: 16px;
      & > em {
        display: none;
      }
    }
  }
}

@media screen and (max-width: @screen-md) {
  .standardList {
    .listContent {
      & > div {
        display: block;
      }
      & > div:last-child {
        top: 0;
        width: 100%;
      }
    }
  }
  .listCard {
    :global {
      .ant-radio-group {
        display: block;
        margin-bottom: 8px;
      }
    }
  }
}

@media screen and (max-width: @screen-lg) and (min-width: @screen-md) {
  .standardList {
    .listContent {
      & > div {
        display: block;
      }
      & > div:last-child {
        top: 0;
        width: 100%;
      }
    }
  }
}

@media screen and (max-width: @screen-xl) {
  .standardList {
    .listContent {
      & > div {
        margin-left: 24px;
      }
      & > div:last-child {
        top: 0;
      }
    }
  }
}

@media screen and (max-width: 1400px) {
  .standardList {
    .listContent {
      text-align: right;
      & > div:last-child {
        top: 0;
      }
    }
  }
}

.standardListForm {
  :global {
    .ant-form-item {
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 32px;
        padding-top: 4px;
      }
    }
  }
}

.formResult {
  width: 100%;
  [class^='title'] {
    margin-bottom: 8px;
  }
}