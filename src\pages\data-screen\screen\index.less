.cardList {
  margin-top: 20px;
  padding-bottom: 24px;
  margin-right: 52px;
}
.cardList button{
  margin-bottom: 30px;
}
.card {
  width: 300px;
  height: 224px;
  margin-right: 10px;
  margin-bottom: 45px;
  position: relative;
}
.card .img{
  width: 100%;
  min-height: 180px;
}
.card img{
  width: 100%;
  min-height: 180px;
}
.edit{
  position: absolute;
  width: 100%;
  height: calc(~"100% - 48px");
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
}
.edit .editBtn{
  width: 50px;
  height: 30px;
  position: absolute;
  line-height: 30px;
  text-align: center;
  background-color: #00BAFF;
  margin-top: 53px;
  margin-left: calc((~"100% - 50px") / 2);
}
.editBtn a{
  color: black;
  text-decoration: none;
}
.footer{
  width: 100%;
  color: white;
  height: 40px;
  background-color: #1d262e;
  display: flex;
}
.title{
  width: 40%;
  line-height: 40px;
  text-align: center;
}
.right{
  width: 60%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.status{
  line-height: 40px;
  height: 40px;
  display: flex;
  width: 100%;
  justify-content: space-evenly;
}
.new{
  width: 300px;
  height: 224px;
  margin-right: 70px;
  margin-bottom: 45px;
  background-color: #00BAFF;
  float: left;
}
.size{
  display: flex;
}
.space{
  width: 40px;
}
.add{
  width: 300px;
  height: 220px;
  text-align: center;
  font-size: 16px;
  border: 2px dashed lightgray;
}
.add:hover{
  border: 2px dashed #00baff;
}

.tableListOperator {
  margin-top: 20px;
  button {
    margin-right: 8px;
  }
}