import React from 'react';
import { Chart, Axis, Tooltip, <PERSON>eom } from 'bizcharts';
import autoHeight from '../autoHeight';
import styles from '../index.less';

export interface IAxis {
  title: any;
  line: any;
  gridAlign: any;
  labels: any;
  tickLine: any;
  grid: any;
}

export interface IMiniAreaProps {
  color?: string;
  height?: number;
  borderColor?: string;
  line?: boolean;
  animate?: boolean;
  xAxis?: IAxis;
  forceFit?: boolean;
  scale?: { x: any; y: any };
  yAxis?: IAxis;
  borderWidth?: number;
  data: Array<{
    x: number | string;
    y: number;
  }>;
}

class MiniArea extends React.Component<IMiniAreaProps> {
  render() {
    const {
      height = 1,
      data = [],
      forceFit = true,
      color = 'rgba(24, 144, 255, 0.2)',
      borderColor = '#1089ff',
      scale = { x: {}, y: {} },
      borderWidth = 2,
      line,
      xAxis,
      yAxis,
      animate = true,
    } = this.props;

    const padding: [number, number, number, number] = [36, 5, 30, 5];

    const scaleProps = {
      x: {
        type: 'timeCat',
        range: [0, 1],
        ...scale!.x,
      },
      y: {
        ...scale!.y,
      },
    };

    const tooltip: [string, (...args: any[]) => { name?: string; value: string }] = [
      'x*y',
      (x: string, y: string) => ({
        name: x,
        value: y,
      }),
    ];

    const chartHeight = height + 54;

    return (
      <div className={styles.miniChart} style={{ height }}>
        <div className={styles.chartContent}>
          {height > 0 && (
            <Chart
              animate={animate}
              scale={scaleProps}
              height={chartHeight}
              forceFit={forceFit}
              data={data}
              padding={padding}
            >
              <Axis
                key="axis-x"
                name="x"
                label={false}
                line={false}
                tickLine={false}
                grid={false}
                {...xAxis}
              />
              <Axis
                key="axis-y"
                name="y"
                label={false}
                line={false}
                tickLine={false}
                grid={false}
                {...yAxis}
              />
              <Tooltip showTitle={false} crosshairs={false} />
              <Geom
                type="area"
                position="x*y"
                color={color}
                tooltip={tooltip}
                shape="smooth"
                style={{
                  fillOpacity: 1,
                }}
              />
              {line ? (
                <Geom
                  type="line"
                  position="x*y"
                  shape="smooth"
                  color={borderColor}
                  size={borderWidth}
                  tooltip={false}
                />
              ) : (
                  <span style={{ display: 'none' }} />
                )}
            </Chart>
          )}
        </div>
      </div>
    );
  }
}

export default autoHeight()(MiniArea);
