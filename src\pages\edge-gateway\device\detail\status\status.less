.cardbox{
    position: relative;
    .cardTop{
        display: flex;
        width: 100%;
        justify-content: space-between;
        height: 30px;
    }
    .cardContent{
        width: 100%;
        height: 100px;
        .contentSize{
            font-size: 30px; 
            font-weight: 600;
            height: 40px;
            line-height: 40px;
        }
        .contentChart{
            display: 'flex'; 
            width: '100%';
            height: 60px;
            line-height: 60px;
            justify-content: 'center';
        }
    }
    .cardBottom{
        display: flex;
        width: 100%;
        height: 20px;
        // border-top: 1px solid lightgray;
        justify-content: flex-start;
    }
}

// import { Field } from "@/pages/analysis/components/Charts";
// import { Card, Col, Icon, Progress, Row, Tooltip } from "antd";
// import React, { useEffect, useState } from "react";
// import styles from './status.less';
// import apis from '@/services';
// import moment from "moment";

// interface Props {
//     deviceId: string;
// }

// interface State {
//     info: any;
//     deviceId: string;
//     properties: any;
// }

// const Status: React.FC<Props> = props => {
//     const initState: State = {
//         info: {},
//         deviceId: props.deviceId || '',
//         properties: []
//     }
//     const [info, setInfo] = useState(initState.info);
//     const [deviceId, setDeviceId] = useState(initState.deviceId);
//     const [properties, setProperties] = useState(initState.properties);
//     const topColResponsiveProps = {
//         xs: 24,
//         sm: 12,
//         md: 12,
//         lg: 12,
//         xl: 6,
//         style: { marginBottom: 24 },
//     };

//     const getInfo = (id: string) => {
//         apis.edgeDevice.info(id).then(res => {
//             if (res.status === 200) {
//                 setInfo(res.result);
//                 if(res.result.metadata){
//                     setProperties(JSON.parse(res.result.metadata).properties);
//                     console.log(JSON.parse(res.result.metadata).properties);
//                 }
//             }
//         })
//     }

//     useEffect(() => {
//         if (props.deviceId) {
//             getInfo(props.deviceId);
//         }
//     }, []);

//     return (
//         <Row gutter={24} id="device-edge-status" >
//             <Col {...topColResponsiveProps}>
//                 <Card bodyStyle={{ padding: '20px 24px 8px 24px' }}>
//                     <div className={styles.cardbox}>
//                         <div className={styles.cardTop}>
//                             <div className={styles.topItem}>设备状态</div>
//                             <div className={styles.topItem}>
//                                 <Tooltip title="刷新">
//                                     <Icon type="sync" onClick={() => {getInfo(deviceId)}} />
//                                 </Tooltip>
//                             </div>
//                         </div>
//                         <div className={styles.cardContent}>
//                             <div className={styles.contentSize}>{info.state?.text}</div>
//                         </div>
//                         <div className={styles.cardBottom}>
//                             <div style={{ float: 'left', width: '100%' }}>
//                                 <Field
//                                     label='上线时间'
//                                     value={info.state?.value !== 'notActive' ? moment(info.onlineTime).format('YYYY-MM-DD HH:mm:ss') : '/'}
//                                 />
//                             </div>
//                         </div>
//                     </div>
//                 </Card>
//             </Col>
//             <Col {...topColResponsiveProps}>
//                 <Card bodyStyle={{ padding: '20px 24px 8px 24px' }}>
//                     <div className={styles.cardbox}>
//                         <div className={styles.cardTop}>
//                             <div className={styles.topItem}>CPU使用率</div>
//                             <div className={styles.topItem}>
//                                 <Tooltip title="刷新">
//                                     <Icon type="sync" onClick={() => { }} />
//                                 </Tooltip>
//                             </div>
//                         </div>
//                         <div className={styles.cardContent}>
//                             <div style={{ display: 'flex', width: '100%', justifyContent: 'center' }}>
//                                 <Progress strokeWidth={15} strokeLinecap="square" status="active" type="circle" percent={75} width={120} />
//                             </div>
//                         </div>
//                         <div className={styles.cardBottom}></div>
//                     </div>
//                 </Card>
//             </Col>
//             <Col {...topColResponsiveProps}>
//                 <Card bodyStyle={{ padding: '20px 24px 8px 24px' }}>
//                     <div className={styles.cardbox}>
//                         <div className={styles.cardTop}>
//                             <div className={styles.topItem}>内存</div>
//                             <div className={styles.topItem}>
//                                 <Tooltip title="刷新">
//                                     <Icon type="sync" onClick={() => { }} />
//                                 </Tooltip>
//                             </div>
//                         </div>
//                         <div className={styles.cardContent}>
//                             <div className={styles.contentSize}>32%</div>
//                             <div className={styles.contentChart}>
//                                 <Progress strokeWidth={20} strokeLinecap="square" percent={75} width={100} />
//                             </div>
//                         </div>
//                         <div className={styles.cardBottom}>
//                             <div style={{ whiteSpace: 'nowrap', width: '100%' }}>
//                                 <div style={{ float: 'left', width: '50%' }}>
//                                     <Field
//                                         label='总量:'
//                                         value={'100%'}
//                                     />
//                                 </div>
//                                 <div style={{ float: 'left', width: '50%' }}>
//                                     <Field
//                                         label='已使用量:'
//                                         value={'28.8%'}
//                                     />
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                 </Card>
//             </Col>
//             <Col {...topColResponsiveProps}>
//                 <Card bodyStyle={{ padding: '20px 24px 8px 24px' }}>
//                     <div className={styles.cardbox}>
//                         <div className={styles.cardTop}>
//                             <div className={styles.topItem}>磁盘</div>
//                             <div className={styles.topItem}>
//                                 <Tooltip title="刷新">
//                                     <Icon type="sync" onClick={() => { }} />
//                                 </Tooltip>
//                             </div>
//                         </div>
//                         <div className={styles.cardContent}>
//                             <div className={styles.contentSize}>25%</div>
//                             <div className={styles.contentChart}>
//                                 <Progress strokeColor={{
//                                     '0%': '#108ee9',
//                                     '100%': '#87d068',
//                                 }} strokeWidth={20} strokeLinecap="square" percent={25} width={100} />
//                             </div>
//                         </div>
//                         <div className={styles.cardBottom}>
//                             <div style={{ whiteSpace: 'nowrap', width: '100%' }}>
//                                 <div style={{ float: 'left', width: '50%' }}>
//                                     <Field
//                                         label='总量:'
//                                         value={'100%'}
//                                     />
//                                 </div>
//                                 <div style={{ float: 'left', width: '50%' }}>
//                                     <Field
//                                         label='已使用量:'
//                                         value={'28.8%'}
//                                     />
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                 </Card>
//             </Col>
//         </Row>
//     )
// }

// export default Status;