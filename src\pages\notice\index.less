@import '~antd/es/style/themes/default.less';

.filterCardList {
  margin-bottom: -24px;

  :global {
    .ant-card-meta-content {
      margin-top: 0;
    }

    // disabled white space
    .ant-card-meta-avatar {
      font-size: 0;
    }

    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
  }

  .cardInfo {
    margin-top: 16px;
    margin-left: 40px;
    zoom: 1;

    &::before,
    &::after {
      display: table;
      content: ' ';
    }

    &::after {
      clear: both;
      height: 0;
      font-size: 0;
      visibility: hidden;
    }

    & > div {
      position: relative;
      float: left;
      width: 50%;
      text-align: left;

      p {
        margin: 0;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
      }

      p:first-child {
        margin-bottom: 4px;
        color: @text-color-secondary;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }
}

.xx {
  .ant-input-number {
    width: 295px;
  }
}

.newButton {
  width: 100%;
  height: 170px;
  color: @text-color-secondary;
  font-weight: 400;
  font-size: large;
  background-color: @component-background;
  border-color: @border-color-base;
  border-radius: @border-radius-sm;
}

.emailEditor {
  :global {
    .bf-content {
      height: 200px;
    }
  }
}
