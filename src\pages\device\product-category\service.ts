import request from '@/utils/request';

export async function query_tree(params: any){
    return request(`/iotservice/device/category/_tree`,{
        method: 'GET',
        params,
    })
}

export async function update(params: any){
    return request(`/iotservice/device/category`,{
        method: 'PATCH',
        data: params,
    })
}

export async function save(params: any){
    return request(`/iotservice/device/category`,{
        method: 'POST',
        data: params,
    })
}

export async function remove(id: any){
    return request(`/iotservice/device/category/${id}`,{
        method: 'DELETE'
    })
}

