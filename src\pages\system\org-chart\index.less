 .orgContainer {
   :global {
    .orgchart{
      background: #fff;
    }
    .orgchart-container{
      border: none;
    }
   }
 }

 .node {
   min-width: 140px;
   border-radius: 3px;
   border: 1px solid #4c77bf;
   display: flex;
   flex-direction: column;
   transition: .3s;
 }

 .node:hover {
   box-shadow: 0px 0px 10px 10px #b1d9ff;
 }

 .top {
   height: 36px;
   color: #fff;
   font-size: 14px;
   background: #4c77bf;
   display: flex;
   align-items: center;
   justify-content: space-between;
   padding: 0 12px;
 }

 .title {
   white-space: nowrap;
   margin-right: 12px;
 }

 .content {
   padding: 3px 12px;
   background: #fff;
   flex: 1 1;
   display: flex;
   justify-content: space-between;
   align-items: center;
 }

 .item>div {
   display: flex;
 }

 .mark {
   width: 50px;
   display: flex;
   justify-content: flex-start;
   font-size: 14px;
   margin-right: 10px;
   white-space: nowrap;
   color: rgba(0, 0, 0, .45);
 }

 .action {
   height: 100%;
   vertical-align: bottom;
   align-self: flex-end;
   padding-bottom: 3px;
   cursor: pointer;
 }
