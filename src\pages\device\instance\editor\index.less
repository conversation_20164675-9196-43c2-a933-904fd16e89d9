@import '~antd/es/style/themes/default.less';
@import '../../../../utils/utils.less';

.cardList {
  margin-bottom: -24px;

  .card {
    :global {
      .ant-card-meta-title {
        margin-bottom: 12px;

        &>a {
          display: inline-block;
          max-width: 100%;
          color: @heading-color;
        }
      }

      .ant-card-body:hover {
        .ant-card-meta-title>a {
          color: @primary-color;
        }
      }
    }
  }

  .item {
    height: 64px;
  }

  :global {
    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
  }
}

.extraImg {
  width: 155px;
  margin-top: -20px;
  text-align: center;

  img {
    width: 100%;
  }
}

.newButton {
  width: 100%;
  height: 188px;
  color: @text-color-secondary;
  background-color: @component-background;
  // border-color: @border-color-base;
  // border-radius: @border-radius-sm;
}

.cardAvatar {
  width: 48px;
  height: 48px;
  border-radius: 48px;
}

.cardDescription {
  .textOverflowMulti();
}

.pageHeaderContent {
  position: relative;
}

.contentLink {
  margin-top: 16px;

  a {
    margin-right: 32px;

    img {
      width: 24px;
    }
  }

  img {
    margin-right: 8px;
    vertical-align: middle;
  }
}

.ant-page-header-footer {
  padding-top: 0px;
}


@media screen and (max-width: @screen-lg) {
  .contentLink {
    a {
      margin-right: 16px;
    }
  }
}

@media screen and (max-width: @screen-md) {
  .extraImg {
    display: none;
  }
}

@media screen and (max-width: @screen-sm) {
  .pageHeaderContent {
    margin-top: 10px;
    // padding-bottom: 10px;
  }

  .contentLink {
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 1000px;

    a {
      margin-right: 16px;
    }

    img {
      margin-right: 4px;
    }
  }
}

.instancePageHeader {
  .ant-pro-page-header-wrap-extraContent {
    margin-bottom: 0px;
  }
}

.debuggerCascader {
  .ant-cascader-picker {
    width: 100%;
  }
}
