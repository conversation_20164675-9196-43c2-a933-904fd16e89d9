import request from '@/utils/request';

export async function configType(params?: any) {
  return request(`/iotservice/notifier/config/types`, {
    method: 'GET',
    params,
  });
}

export async function template(params?: any) {
  return request(`/iotservice/notifier/template/_query`, {
    method: 'GET',
    params,
  });
}
export async function queryById(id: string) {
  return request(`/iotservice/notifier/template/${id}`, {
    method: 'GET',
  });
}

export async function saveOrUpdate(item: any) {
  return request(`/iotservice/notifier/template`, {
    method: 'PATCH',
    data: item,
  });
}

export async function saveOrUpdateConfig(item: any) {
  return request(`/iotservice/notifier/config`, {
    method: 'PATCH',
    data: item,
  });
}

export async function removeConfig(id: string) {
  return request(`/iotservice/notifier/config/${id}`, {
    method: 'DELETE',
  });
}

export async function remove(id: string) {
  return request(`/iotservice/notifier/template/${id}`, {
    method: 'DELETE',
  });
}

export async function config(params?: any) {
  return request(`/iotservice/notifier/config/_query`, {
    method: 'GET',
    params,
  });
}

export async function queryConfigById(id: string) {
  return request(`/iotservice/notifier/config/${id}`, {
    method: 'GET',
  });
}

export async function configMetadata(type: string, id: string) {
  return request(`/iotservice/notifier/config/${type}/${id}/metadata`, {
    method: 'GET',
  });
}

export async function debugTemplate(id: string, data: any) {
  return request(`/iotservice/notifier/${id}/_send`, {
    method: 'POST',
    data,
  });
}

async function list(params: any) {
  return request(`/iotservice/notify/history/_query`, {
    method: 'GET',
    params
  })
}

async function detail(id: string) {
  return request(`/iotservice/notify/history/${id}`, {
    method: 'GET'
  })
}

export const history = { list, detail };
