// // .login * {
// //   font-family: "微软雅黑";
// //   font-size: 16px;
// //   border: 0;
// //   padding: 0;
// //   margin: 0;
// //   color: #666;
// //   box-sizing: border-box;
// //   -moz-box-sizing: border-box;
// //   -webkit-box-sizing: border-box;
// // }
html, body{
  padding: 0;
  margin: 0;
}
.login{
  width: 80%;
  height: 680px;
  min-width: 1100px;
  max-width: 1500px;
  background: url(./img/login-bg.png) repeat;
  background-size: 100% 100%;
  position: relative;
  margin: calc((100vh - 680px) / 2) auto;
}
.bg1{
  position: absolute;
  top: 50%;
  left: 25%;
  transform: translate(-50%,-50%);
  .title {
    width: 350px;
    font-size: 36px;
    color: #409EFF;
    white-space: nowrap;
    margin-bottom: 100px;
  }
  img{
    width: 360px;
  }
}
.gyl{
  width: 510px;
  height: 237px;
  color: #FFFFFF;
  font-size: 72px;
  position: absolute;
  padding-top: 20px;
  left: 15%;
  top: 0px;
  bottom: 0px;
  margin: auto;
}
.gy2{
  color: #fff;
  margin-left: 6px;
  font-size: 18px;
  text-align: center;
  margin-top: 10px;
}
.box{
  width: 420px;
  position: absolute;
  top: 0px;
  right: 25%;
  transform: translateX(50%);
  bottom: 0px;
  margin: auto;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.box1{
  width: 420px;
  padding: 60px 40px 50px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 2px 2px 10px 10px #faf9f9;
  text-align: center;
}
.header{
  width: 100%;
  height: 35px;
  font-size: 20px;
  color: #0081ff;
  text-align: center;
}
.item{
  width: 100%;
  display: flex;
  margin: 10px 0px;
  height: 40px;
  position: relative;
}

.item input {
  border-bottom: 0.0625rem solid #347ebc;
  height: 2.5625rem;
  width: 100%;
  text-indent: 1.125rem;
  outline: none;
}

.userLabel {
  border-bottom: 1px solid #347ebc;
  height: 41px;
  width: 60px;
  line-height: 41px;
}

.remember{
  display: flex;
  margin: 10px 0px;
  height: 40px;
  align-items: center;
}
.remember_box{
  width: 200px;
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: flex-start;
}
.remember .text{
  margin-left: 10px;
  width: 100px;
}
.login .btn {
  border: none;
  color: #fff;
  width: 100%;
  text-align: center;
  background-color: #409eff;
  text-indent: 0rem;
  height: 40px;
  box-shadow: 0px 0px 16px 0px rgba(59, 133, 246, 0.34);
  border-radius: 15px;
}
.avatar{
  width: 100%;
  display: flex;
  justify-content: center;
  height: 150px;
  .avatarx{
    width: 100px;
    height: 100px;
    border: 1px solid lightgray;
    border-radius: 50%;
  }
}
.code{
  width: 85px;
  height: 35px;
  position: absolute;
  display: flex;
  justify-content: space-around;
  right: 1.2rem;
}
.code_img{
  width: 80px;
  height: 35px;
}
@media (min-width:0px) and (max-width:1400px) {
  .box{
    width: 340px;
  }
  .gyl{
   width: 400px;
  }
}

@media (min-width:400px) and (max-width:800px) {
  .box{
    right: 20%;
    width: 320px;
    top: 0px;
    right: 0px;
    bottom: 0px;
    margin: auto;
    left: 0px;
  }
   .gyl{
    position: absolute;
    top: 40px;
    font-size: 72px;
    width: 100%;
    text-align: center;
    height: 200px;
    margin: 0 auto;
    left: 0;
    right: 0;
   }
   .gy2{
    width: 100%;
    padding: 0 50px;
    text-align: center;
    font-size: 13px;
    height: 60px;
   }
}

@media (min-width:330px) and (max-width:420px) {
  .box{
    right: 20%;
    width: 300px;
    top: 20px;
    right: 0px;
    bottom: 0px;
    margin: auto;
    left: 0px;
  }
  .gyl{
    position: absolute;
    top: 6%;
    font-size: 36px;
    width: 100%;
    text-align: center;
    height: 200px;
    margin: 0 auto;
    left: 0;
    right: 0;
   }
   .gy2{
    width: 100%;
    padding: 0 50px;
    text-align: center;
    font-size: 13px;
    height: 60px;
    color: rgba(0, 0, 0, 0);
   }
}
@media (min-width:0px) and (max-width:330px) {
  .box{
    right: 20%;
    width: 280px;
    top: 20px;
    right: 0px;
    bottom: 0px;
    margin: auto;
    left: 0px;
  }
  .gyl{
    position: absolute;
    top: 10%;
    font-size: 36px;
    width: 100%;
    text-align: center;
    height: 200px;
    margin: 0 auto;
    left: 0;
    right: 0;
   }
   .gy2{
    width: 100%;
    padding: 0 50px;
    text-align: center;
    font-size: 13px;
    height: 60px;
    color: rgba(0, 0, 0, 0);
   }
}

@media (max-height: 600px) {
  .gyl{
    top: 0;
    margin: 0 auto;
    left: 0;
    right: 0;
    position: absolute;
   }
}
