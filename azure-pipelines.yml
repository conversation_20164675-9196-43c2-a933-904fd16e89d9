# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
name: ant design pro

trigger:
  - master

jobs:
  - job: lintAndBuild

    pool:
      vmImage: 'Ubuntu-16.04'

    steps:
      - checkout: self
        clean: false
      - script: npm install
        displayName: install
      - script: npm run lint
        displayName: lint
      - script: npm run tsc
        displayName: tsc
      - script: npm run build
        env:
          PROGRESS: none
        displayName: build

  - job: test
    pool:
      vmImage: 'Ubuntu-16.04'

    container:
      image: circleci/node:latest-browsers
      options: '-u root'

    steps:
      - script: npm install
        displayName: install
      - script: npm run test:all
        env:
          PROGRESS: none
          UMI_UI: none
        displayName: test

  - job: Windows
    pool:
      vmImage: 'win1803'
    steps:
      - task: NodeTool@0
        inputs:
          versionSpec: '11.x'
      - script: npm install
        displayName: install
      - script: npm run lint
        displayName: lint
      - script: npm run tsc
        displayName: tsc
      - script: npm run test:all
        env:
          PROGRESS: none
          UMI_UI: none
        displayName: test
      - script: npm run build
        env:
          PROGRESS: none
        displayName: build

  - job: MacOS
    pool:
      vmImage: 'macOS-latest'
    steps:
      - task: NodeTool@0
        inputs:
          versionSpec: '11.x'
      - script: npm install
        displayName: install
      - script: npm run lint
        displayName: lint
      - script: npm run tsc
        displayName: tsc
      - script: npm run
        env:
          PROGRESS: none
          UMI_UI: none
        displayName: build
