/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  dev: {
    '/iotservice': {
      // target: 'http://192.168.22.222:8844/',
      // ws: 'ws://192.168.22.222:8844/',
      // target: 'http://water.zlkjhb.com:9000/iotservice',
      // ws: 'http://water.zlkjhb.com:9000/iotservice',
      // ws: 'ws://test.rt-iot.cn/iotservice',
      // target: 'http://test.rt-iot.cn/iotservice',
      target: 'http://172.10.20.250:7091/',
      // target: 'http://127.0.0.1:8180/',
      // ws: 'ws://127.0.0.1:8180/',
      ws: 'ws://172.10.20.250:7091/',
      // ws: 'ws://demo.rt-iot.cn/iotservice',
      // target: 'http://demo.rt-iot.cn/iotservice',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  },
  test: {
    '/iotservice': {
      target: 'http://127.0.0.1:8180/',
      ws: 'ws://127.0.0.1:8180/',
      // target: 'http://2.rt-iot.org:9010/',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  },
  pre: {
    '/iotservice': {
      // target: 'http://192.168.3.89:8848/',
      target: 'http://127.0.0.1:8180/',
      ws: 'ws://127.0.0.1:8180/',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  },
  build: {
    '/iotservice': {
      target: 'http://172.10.20.183:7091/',
      ws: 'ws://172.10.20.183:7091/',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  },
  mgs: {  //莫干山环境
    '/iotservice': {
      target: 'http://192.169.254.52:7091/',
      ws: 'ws://192.169.254.52:7091/',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  }
};
