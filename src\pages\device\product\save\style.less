@import '~antd/es/style/themes/default.less';

.main {
  :global {
    .ant-descriptions-row>td {
      padding-bottom: 8px;
    }

    .ant-page-header-heading-extra {
      flex-direction: column;
    }
  }
}

.headerList {
  margin-bottom: 4px;

  :global {
    .ant-descriptions-row>td {
      padding-bottom: 8px;
    }
  }

  .stepDescription {
    position: relative;
    left: 38px;
    padding-top: 8px;
    font-size: 14px;
    text-align: left;

    >div {
      margin-top: 8px;
      margin-bottom: 4px;
    }
  }
}

.pageHeader {
  :global {
    .ant-page-header-heading-extra>*+* {
      margin-left: 8px;
    }
  }

  .moreInfo {
    display: flex;
    justify-content: space-between;
    width: 200px;
  }
}

@media screen and (max-width: @screen-sm) {
  .stepDescription {
    left: 8px;
  }

  .pageHeader {
    :global {
      .ant-pro-page-header-wrap-row {
        flex-direction: column;
      }
    }
  }
}

.avatar_title {
  height: 22px;
  margin-bottom: 8px;
  color: @heading-color;
  font-size: @font-size-base;
  line-height: 22px;
}
.avatar {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
  overflow: hidden;
  img {
    width: 100%;
  }
}
.button_view {
  width: 144px;
}
