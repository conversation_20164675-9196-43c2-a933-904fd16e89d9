@import '~antd/es/style/themes/default.less';

.filterCardList {
  margin-bottom: -24px;
  :global {
    .ant-card-meta-content {
      margin-top: 0;
    }
    // disabled white space
    .ant-card-meta-avatar {
      font-size: 0;
    }

    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
    .ant-list .ant-list-item{
      min-width: 230px;
    }
  }
  .cardInfo {
    margin-top: 16px;
    zoom: 1;
    &::before,
    &::after {
      display: table;
      content: ' ';
    }
    &::after {
      clear: both;
      height: 0;
      font-size: 0;
      visibility: hidden;
    }
    & > div {
      position: relative;
      float: left;
      width: 50%;
      text-align: left;
      p {
        margin: 0;
        font-size: 12px;
        line-height: 20px;
        font-weight: 500;
      }
      p:first-child {
        margin-bottom: 4px;
        color: @text-color-secondary;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }
}

.xx{
  .ant-input-number{
    width: 295px;
  }
}

.newButton {
  width: 100%;
  height: 178px;
  color: @text-color-secondary;
  background-color: @component-background;
  border-color: @border-color-base;
  border-radius: @border-radius-sm;
  font-size:large;
  font-weight: 400;
}
