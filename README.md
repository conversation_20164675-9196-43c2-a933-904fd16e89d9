## 使用

```bash
$ git clone http://*************:3008/rutong/ironman-web.git
$ npm install
$ npm start         # visit http://localhost:8000
```
>> 推荐使用淘宝镜像

### 本地开发环境要求

- nodeJs v12.x
- npm v6.x
- Chrome v80.0+

⚠️本项目基于[Ant Design 3.x](https://3x.ant.design/index-cn)开发,二次开发需要对Ant Design有一定基础。

本地开发项目建议使用如下命令启动项目

```bash
$ npm run start:dev     //支持：dev、test、pre环境
```

项目多处采用了 SSE 接口交互，开发需要使用 dev 环境变量（生产环境使用 nginx 代理了 EventSource 接口）

### 修改后台接口地址

后台接口配置文件地址：`config/proxy.ts`:

```typescript
/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 */
export default {
  dev: {
    '/iotservice': {
      target: '后台地址',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  },
  test: {
    '/iotservice': {
      target: '后台地址',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  },
  pre: {
    '/iotservice': {
      target: '后台地址',
      changeOrigin: true,
      pathRewrite: { '^/iotservice': '' },
    },
  },
};
```

> **注：本项目所有接口采用“/iotservice”开头，用于统一代理**

更多信息请参考 [使用文档](http://doc.rt-iot.cn)。

## 支持环境

## 参与贡献

我们非常欢迎你的贡献，你可以通过以下方式和我们一起共建 :smiley:：

