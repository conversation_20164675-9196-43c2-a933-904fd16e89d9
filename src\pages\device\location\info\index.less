@import '../../../../../node_modules/antd/es/style/themes/default.less';

.filterCardList {
  margin-bottom: -24px;
  :global {
    .ant-card-meta-content {
      margin-top: 0;
    }
    // disabled white space
    .ant-card-meta-avatar {
      font-size: 0;
    }

    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
  }
  .cardInfo {
    margin-top: 16px;
    margin-left: 40px;
    zoom: 1;
    &::before,
    &::after {
      display: table;
      content: ' ';
    }
    &::after {
      clear: both;
      height: 0;
      font-size: 0;
      visibility: hidden;
    }
    & > div {
      position: relative;
      float: left;
      width: 50%;
      text-align: left;
      p {
        margin: 0;
        font-size: 12px;
        line-height: 20px;
        font-weight: 500;
      }
      p:first-child {
        margin-bottom: 4px;
        color: @text-color-secondary;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }
}

.xx{
  .ant-input-number{
    width: 295px;
  }
}

.newButton {
  width: 100%;
  height: 170px;
  color: @text-color-secondary;
  background-color: @component-background;
  border-color: @border-color-base;
  border-radius: @border-radius-sm;
  font-size:large;
  font-weight: 400;
}

.demo-infinite-container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: auto;
  padding: 8px 24px;
  height: 300px;
}
.demo-loading-container {
  position: absolute;
  bottom: 40px;
  width: 100%;
  text-align: center;
}
[data-theme="dark"] .demo-infinite-container {
  border: 1px solid #303030;
}

.col {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
}
