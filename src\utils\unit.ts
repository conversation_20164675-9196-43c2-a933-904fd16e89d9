export const units: Unit[] = [
  {
    symbol: '%',
    typeText: '常用单位',
    name: '百分比',
    description: '百分比(%)',
    id: 'percent',
    type: 'common',
  },
  {
    symbol: 'count',
    typeText: '常用单位',
    name: '次',
    description: '次',
    id: 'count',
    type: 'common',
  },
  {
    symbol: 'turn/m',
    typeText: '常用单位',
    name: '转每分钟',
    description: '转每分钟',
    id: 'turnPerSeconds',
    type: 'common',
  },
  {
    symbol: 'nm',
    typeText: '计量单位',
    name: '纳米',
    description: '长度单位:纳米(nm)',
    id: 'nanometer',
    type: 'length',
  },
  {
    symbol: 'μm',
    typeText: '计量单位',
    name: '微米',
    description: '长度单位:微米(μm)',
    id: 'micron',
    type: 'length',
  },
  {
    symbol: 'mm',
    typeText: '计量单位',
    name: '毫米',
    description: '长度单位:毫米(mm)',
    id: 'millimeter',
    type: 'length',
  },
  {
    symbol: 'cm',
    typeText: '计量单位',
    name: '厘米',
    description: '长度单位:厘米(cm)',
    id: 'centimeter',
    type: 'length',
  },
  {
    symbol: 'm',
    typeText: '计量单位',
    name: '米',
    description: '长度单位:米(m)',
    id: 'meter',
    type: 'length',
  },
  {
    symbol: 'km',
    typeText: '计量单位',
    name: '千米',
    description: '长度单位:千米(km)',
    id: 'kilometer',
    type: 'length',
  },
  {
    symbol: 'mm²',
    typeText: '面积单位',
    name: '平方毫米',
    description: '面积单位:平方毫米(mm²)',
    id: 'squareMillimeter',
    type: 'area',
  },
  {
    symbol: 'cm²',
    typeText: '面积单位',
    name: '平方厘米',
    description: '面积单位:平方厘米(cm²)',
    id: 'squareCentimeter',
    type: 'area',
  },
  {
    symbol: 'm²',
    typeText: '面积单位',
    name: '平方米',
    description: '面积单位:平方米(m²)',
    id: 'squareMeter',
    type: 'area',
  },
  {
    symbol: 'km²',
    typeText: '面积单位',
    name: '平方千米',
    description: '面积单位:平方千米(km²)',
    id: 'squareKilometer',
    type: 'area',
  },
  {
    symbol: 'hm²',
    typeText: '面积单位',
    name: '公顷',
    description: '面积单位:公顷(hm²)',
    id: 'hectare',
    type: 'area',
  },
  {
    symbol: 'd',
    typeText: '时间单位',
    name: '天',
    description: '时间单位:天(d)',
    id: 'days',
    type: 'time',
  },
  {
    symbol: 'h',
    typeText: '时间单位',
    name: '小时',
    description: '时间单位:小时(h)',
    id: 'hour',
    type: 'time',
  },
  {
    symbol: 'min',
    typeText: '时间单位',
    name: '分钟',
    description: '时间单位:分钟(m)',
    id: 'minutes',
    type: 'time',
  },
  {
    symbol: 's',
    typeText: '时间单位',
    name: '秒',
    description: '时间单位:秒(s)',
    id: 'seconds',
    type: 'time',
  },
  {
    symbol: 'ms',
    typeText: '时间单位',
    name: '毫秒',
    description: '时间单位:毫秒(ms)',
    id: 'milliseconds',
    type: 'time',
  },
  {
    symbol: 'μs',
    typeText: '时间单位',
    name: '微秒',
    description: '时间单位:微秒(μs)',
    id: 'microseconds',
    type: 'time',
  },
  {
    symbol: 'ns',
    typeText: '时间单位',
    name: '纳秒',
    description: '时间单位:纳秒(ns)',
    id: 'nanoseconds',
    type: 'time',
  },
  {
    symbol: 'mm³',
    typeText: '容积单位',
    name: '立方毫米',
    description: '体积单位:立方毫米(mm³)',
    id: 'cubicMillimeter',
    type: 'volume',
  },
  {
    symbol: 'cm³',
    typeText: '容积单位',
    name: '立方厘米',
    description: '体积单位:立方厘米(cm³)',
    id: 'cubicCentimeter',
    type: 'volume',
  },
  {
    symbol: 'm³',
    typeText: '容积单位',
    name: '立方米',
    description: '体积单位:立方米(m³)',
    id: 'cubicMeter',
    type: 'volume',
  },
  {
    symbol: 'km³',
    typeText: '容积单位',
    name: '立方千米',
    description: '体积单位:立方千米(km³)',
    id: 'cubicKilometer',
    type: 'volume',
  },
  {
    symbol: 'mL',
    typeText: '其他',
    name: '毫升',
    description: '容积单位:毫升(mL)',
    id: 'milliliter',
    type: 'capacity',
  },
  {
    symbol: 'L',
    typeText: '其他',
    name: '升',
    description: '容积单位:升(L)',
    id: 'litre',
    type: 'capacity',
  },
  {
    symbol: 'mg',
    typeText: '力单位',
    name: '毫克',
    description: '重量单位:毫克(mg)',
    id: 'milligram',
    type: 'mass',
  },
  {
    symbol: 'g',
    typeText: '力单位',
    name: '克',
    description: '重量单位:克(g)',
    id: 'gramme',
    type: 'mass',
  },
  {
    symbol: 'kg',
    typeText: '力单位',
    name: '千克',
    description: '重量单位:千克(kg)',
    id: 'kilogram',
    type: 'mass',
  },
  {
    symbol: 't',
    typeText: '力单位',
    name: '吨',
    description: '重量单位:吨(t)',
    id: 'ton',
    type: 'mass',
  },
  {
    symbol: 'N',
    typeText: '压力单位',
    name: '牛顿',
    description: '力单位:牛顿(N)',
    id: 'newton',
    type: 'force',
  },
  {
    symbol: 'Pa',
    typeText: '其他',
    name: '帕斯卡',
    description: '压力单位:帕斯卡(Pa)',
    id: 'pascal',
    type: 'pressure',
  },
  {
    symbol: 'kPa',
    typeText: '其他',
    name: '千帕斯卡',
    description: '压力单位:千帕斯卡(kPa)',
    id: 'kiloPascal',
    type: 'pressure',
  },
  {
    symbol: 'K',
    typeText: '温度单位',
    name: '开尔文',
    description: '温度单位:开尔文(K)',
    id: 'kelvin',
    type: 'temperature',
  },
  {
    symbol: '℃',
    typeText: '温度单位',
    name: '摄氏度',
    description: '温度单位:摄氏度(℃)',
    id: 'celsiusDegrees',
    type: 'temperature',
  },
  {
    symbol: '℉',
    typeText: '温度单位',
    name: '华氏度',
    description: '温度单位:华氏度(℉)',
    id: 'fahrenheit',
    type: 'temperature',
  },
  {
    symbol: 'J',
    typeText: '其他',
    name: '焦耳',
    description: '能单位:焦耳(J)',
    id: 'joule',
    type: 'pressure',
  },
  {
    symbol: 'eV',
    typeText: '其他',
    name: '电子伏',
    description: '能单位:电子伏(eV)',
    id: 'electronVolts',
    type: 'pressure',
  },
  {
    symbol: 'kW·h',
    typeText: '其他',
    name: '千瓦·时',
    description: '能单位:千瓦·时(kW·h)',
    id: 'kWattsHour',
    type: 'pressure',
  },
  {
    symbol: 'cal',
    typeText: '其他',
    name: '卡',
    description: '能单位:卡(cal)',
    id: 'cal',
    type: 'pressure',
  },
  {
    symbol: 'W',
    typeText: '功率单位',
    name: '瓦特',
    description: '功率单位:瓦特(W)',
    id: 'watt',
    type: 'power',
  },
  {
    symbol: 'kW',
    typeText: '功率单位',
    name: '千瓦特',
    description: '功率单位:千瓦特(kW)',
    id: 'kilowatt',
    type: 'power',
  },
  {
    symbol: 'rad',
    typeText: '角度单位',
    name: '弧度',
    description: '角度单位:弧度(rad)',
    id: 'radian',
    type: 'angle',
  },
  {
    symbol: '°',
    typeText: '角度单位',
    name: '度',
    description: '角度单位:度(°)',
    id: 'degrees',
    type: 'angle',
  },
  {
    symbol: '′',
    typeText: '角度单位',
    name: '[角]分',
    description: '角度单位:分(′)',
    id: 'fen',
    type: 'angle',
  },
  {
    symbol: '″',
    typeText: '角度单位',
    name: '[角]秒',
    description: '角度单位:度(″)',
    id: 'angleSeconds',
    type: 'angle',
  },
  {
    symbol: 'Hz',
    typeText: '频率单位',
    name: '赫兹',
    description: '频率单位:赫兹(Hz)',
    id: 'hertz',
    type: 'frequency',
  },
  {
    symbol: 'MHz',
    typeText: '频率单位',
    name: '兆赫兹',
    description: '频率单位:兆赫兹(MHz)',
    id: 'megahertz',
    type: 'frequency',
  },
  {
    symbol: 'GHz',
    typeText: '频率单位',
    name: 'G赫兹',
    description: '频率单位:G赫兹(GHz)',
    id: 'ghertz',
    type: 'frequency',
  },
  {
    symbol: 'm/s',
    typeText: '速度单位',
    name: '米每秒',
    description: '速度单位:米每秒(m/s)',
    id: 'mPerSec',
    type: 'speed',
  },
  {
    symbol: 'km/h',
    typeText: '速度单位',
    name: '千米每小时',
    description: '速度单位:千米每小时(km/h)',
    id: 'kmPerHr',
    type: 'speed',
  },
  {
    symbol: 'kn',
    typeText: '速度单位',
    name: '节',
    description: '速度单位:节(kn)',
    id: 'knots',
    type: 'speed',
  },
];

export interface Unit {
  id: string;
  name: string;
  symbol: string;
  description: string;
  type: string;
  typeText: string;
}
