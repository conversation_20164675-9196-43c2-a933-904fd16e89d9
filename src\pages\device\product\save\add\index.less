@import '~antd/es/style/themes/default.less';

.main {
  display: flex;
  width: 100%;
  height: 100%;
  padding-top: 16px;
  padding-bottom: 16px;
  overflow: auto;
  background-color: @menu-bg;
  .right {
    flex: 1;
    padding-top: 8px;
    padding-right: 40px;
    padding-bottom: 8px;
    padding-left: 40px;
    .title {
      margin-bottom: 12px;
      color: @heading-color;
      font-weight: 500;
      font-size: 20px;
      line-height: 28px;
    }
  }
  :global {
    .ant-list-split .ant-list-item:last-child {
      border-bottom: 1px solid @border-color-split;
    }
    .ant-list-item {
      padding-top: 14px;
      padding-bottom: 14px;
    }
  }
}
:global {
  .ant-list-item-meta {
    // 账号绑定图标
    .taobao {
      display: block;
      color: #ff4000;
      font-size: 48px;
      line-height: 48px;
      border-radius: @border-radius-base;
    }
    .dingding {
      margin: 2px;
      padding: 6px;
      color: #fff;
      font-size: 32px;
      line-height: 32px;
      background-color: #2eabff;
      border-radius: @border-radius-base;
    }
    .alipay {
      color: #2eabff;
      font-size: 48px;
      line-height: 48px;
      border-radius: @border-radius-base;
    }
  }

  // 密码强度
  font.strong {
    color: @success-color;
  }
  font.medium {
    color: @warning-color;
  }
  font.weak {
    color: @error-color;
  }
}

@media screen and (max-width: @screen-md) {
  .main {
    flex-direction: column;
    .leftMenu {
      width: 100%;
      border: none;
    }
    .right {
      padding: 40px;
    }
  }
}


.baseView {
    display: flex;
    padding: 12px 0 30px 0;
    :global {
      .ant-legacy-form-item .ant-legacy-form-item-control-wrapper {
        width: 100%;
      }
    }

    .left {
      min-width: 45%;
      max-width: 80%;
    }
    .right {
      flex: 1;
      padding-left: 104px;
      .avatar_title {
        height: 22px;
        margin-bottom: 8px;
        color: @heading-color;
        font-size: @font-size-base;
        line-height: 22px;
      }
      .avatar {
        width: 144px;
        height: 144px;
        margin-bottom: 12px;
        overflow: hidden;
        img {
          width: 100%;
        }
      }
      .button_view {
        width: 144px;
        text-align: center;
      }
    }
  }

  @media screen and (max-width: @screen-xl) {
    .baseView {
      // 漂浮
      flex-direction: column-reverse;

      .right {
        display: flex;
        flex-direction: column;
        align-items: center;
        max-width: 448px;
        padding: 20px;
        .avatar_title {
          display: none;
        }
      }
    }
  }
