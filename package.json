{"name": "IOT", "version": "1.10.0", "private": true, "description": "IOT 前端", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "cross-env REACT_APP_ENV=build umi build", "build:mgs": "cross-env REACT_APP_ENV=mgs umi build", "deploy": "npm run site && npm run gh-pages", "fetch:blocks": "pro fetch-blocks && npm run prettier", "format-imports": "import-sort --write '**/*.{js,jsx,ts,tsx}'", "functions:build": "netlify-lambda build ./lambda", "functions:run": "cross-env NODE_ENV=dev netlify-lambda serve ./lambda", "gh-pages": "cp CNAME ./dist/ && gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "check-prettier lint", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"**/*\"", "site": "npm run fetch:blocks && npm run build && npm run functions:build", "start": "umi dev UMI_UI=none", "dev": "SET NODE_OPTIONS=--openssl-legacy-provider && npm run start:dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none umi dev", "start:no-mock": "cross-env MOCK=none umi dev", "start:no-ui": "cross-env UMI_UI=none umi dev", "start:pre": "cross-env REACT_APP_ENV=pre MOCK=none umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none umi dev", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc", "ui": "umi ui", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.2.2", "@ant-design/pro-layout": "^4.11.4", "@ant-design/pro-table": "^1.2.6", "@antv/data-set": "^0.11.7", "@dabeng/react-orgchart": "^1.0.0", "@formily/antd": "^1.3.3", "@formily/antd-components": "^1.3.3", "@loadable/component": "^5.13.2", "@types/loadable__component": "^5.13.1", "@types/pubsub-js": "^1.8.1", "@types/react-copy-to-clipboard": "^4.3.0", "@types/react-grid-layout": "^0.17.2", "@types/retry": "^0.12.0", "ace-builds": "^1.4.12", "ahooks": "^2.6.1", "antd": "^3.26.18", "bizcharts": "^3.5.9", "bizcharts-plugin-slider": "^2.1.1-beta.1", "braft-editor": "^2.3.8", "classnames": "^2.2.6", "dva": "^2.6.0-beta.21", "event-source-polyfill": "^1.0.20", "export-from-json": "^1.3.4", "form-render": "^0.8.7", "gg-editor": "^2.0.4", "lodash": "^4.17.20", "lodash-decorators": "^6.0.1", "moment": "^2.29.0", "monaco-ace-tokenizer": "^0.2.1", "monaco-editor": "^0.23.0", "monaco-editor-webpack-plugin": "^3.0.1", "numeral": "^2.0.6", "omit.js": "^1.0.2", "path-to-regexp": "2.4.0", "pubsub-js": "^1.9.0", "qs": "^6.9.4", "re-resizable": "^6.6.1", "react": "^16.13.1", "react-ace": "^9.1.4", "react-amap": "^1.2.8", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^16.13.1", "react-fittext": "^1.0.0", "react-grid-layout": "^0.18.3", "react-helmet": "^5.2.1", "react-helmet-async": "^1.0.7", "react-markdown": "^6.0.0", "react-monaco-editor": "^0.32.1", "react-resizable": "^1.11.0", "react-rnd": "^10.2.3", "react-sortable-pane": "^1.1.0", "react-use-websocket": "^2.1.1", "redux": "^4.0.1", "retry": "^0.12.0", "rxjs": "^6.6.3", "tree-tool": "^1.1.8", "umi": "^2.13.13", "umi-plugin-antd-theme": "^1.0.1", "umi-plugin-pro-block": "^1.3.2", "umi-plugin-react": "^1.15.8", "umi-request": "^1.3.5", "video.js": "^7.8.4", "videojs-flash": "^2.2.1"}, "devDependencies": {"@ant-design/pro-cli": "^1.0.23", "@commitlint/config-conventional": "^8.3.4", "@types/classnames": "^2.2.7", "@types/express": "^4.17.8", "@types/history": "^4.7.8", "@types/jest": "^25.2.3", "@types/lodash": "^4.14.161", "@types/numeral": "0.0.28", "@types/qs": "^6.9.5", "@types/react": "^16.9.49", "@types/react-dom": "^16.9.8", "@types/react-helmet": "^5.0.16", "@types/styled-components": "^5.1.3", "@types/video.js": "^7.3.11", "@umijs/fabric": "^2.2.2", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "^3.0.0", "check-prettier": "^1.0.3", "conventional-changelog-cli": "^2.1.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "cz-conventional-changelog": "^3.3.0", "enzyme": "^3.9.0", "eslint-plugin-react-hooks": "^2.5.1", "express": "^4.17.1", "gh-pages": "^2.0.1", "import-sort-cli": "^6.0.0", "import-sort-parser-babylon": "^6.0.0", "import-sort-parser-typescript": "^6.0.0", "import-sort-style-module": "^6.0.0", "jest-puppeteer": "^4.2.0", "jsdom-global": "^3.0.2", "lint-staged": "^10.4.0", "mockjs": "^1.0.1-beta3", "netlify-lambda": "^1.4.13", "node-fetch": "^2.6.1", "prettier": "^1.19.1", "pro-download": "1.0.1", "serverless-http": "^2.6.0", "stylelint": "^13.7.2", "umi-plugin-antd-icon-config": "^1.0.2", "umi-plugin-ga": "^1.1.8", "umi-plugin-pro": "^1.0.2", "umi-types": "^0.5.14"}, "optionalDependencies": {"puppeteer": "^2.0.0"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "create-umi": {"ignoreScript": ["docker*", "functions*", "site", "generateMock"], "ignoreDependencies": ["netlify*", "serverless"], "ignore": [".dockerignore", ".git", ".github", ".gitpod.yml", "CODE_OF_CONDUCT.md", "Dockerfile", "Dockerfile.*", "lambda", "LICENSE", "netlify.toml", "README.*.md", "azure-pipelines.yml", "docker", "CNAME", "create-umi"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}