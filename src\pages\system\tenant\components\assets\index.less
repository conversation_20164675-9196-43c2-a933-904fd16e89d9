@import '~antd/es/style/themes/default.less';
@import '../../../../../utils/utils.less';

.cardList {
  margin-top: 40px;
  margin-bottom: -24px;
  margin-right: 52px;

  .card {
    :global {
      .ant-card-meta-title {
        margin-bottom: 12px;
        & > a {
          display: inline-block;
          max-width: 100%;
          color: @heading-color;
        }
      }
      .ant-card-body:hover {
        .ant-card-meta-title > a {
          color: @primary-color;
        }
      }
    }
  }
  .item {
    height: 64px;
  }

  .newButton {
    width: 100%;
    height: 188px;
    color: @text-color-secondary;
    background-color: @component-background;
    border-color: @border-color-base;
    border-radius: 4px;
  }

  :global {
    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
  }
}

.extraImg {
  width: 155px;
  margin-top: -20px;
  text-align: center;
  img {
    width: 100%;
  }
}

.cardAvatar {
  width: 48px;
  height: 48px;
  border-radius: 48px;
}

.cardDescription {
  .textOverflowMulti();
}

.pageHeaderContent {
  position: relative;
}

.contentLink {
  margin-top: 16px;
  a {
    margin-right: 32px;
    img {
      width: 24px;
    }
  }
  img {
    margin-right: 8px;
    vertical-align: middle;
  }
}

.cardInfo {
  margin-top: 16px;
  margin-left: 40px;
  zoom: 1;
  &::before,
  &::after {
    display: table;
    content: ' ';
  }
  &::after {
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
  }
  & > div {
    position: relative;
    float: left;
    width: 50%;
    text-align: left;
    p {
      margin: 0;
      font-size: 14px;
      line-height: 20px;
      font-weight: 500;
    }
    p:first-child {
      margin-bottom: 4px;
      color: @text-color-secondary;
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.actionForm{
  // background-color: sienna;
  // :global(.ant-form-item) {
  //     margin-bottom: 1px;
  // }
  :global{
      .ant-form-item{
          margin-bottom: 0;
          .ant-form-item-control-wrapper{
              .ant-form-item-control{
                  line-height: 0px;
              }
          }
      }
  }
}

@media screen and (max-width: @screen-lg) {
  .contentLink {
    a {
      margin-right: 16px;
    }
  }
}
@media screen and (max-width: @screen-md) {
  .extraImg {
    display: none;
  }
}

@media screen and (max-width: @screen-sm) {
  .pageHeaderContent {
    padding-bottom: 30px;
  }
  .contentLink {
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 1000px;
    a {
      margin-right: 16px;
    }
    img {
      margin-right: 4px;
    }
  }
}
